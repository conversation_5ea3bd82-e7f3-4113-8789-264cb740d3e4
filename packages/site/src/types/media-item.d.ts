type MediaItemStatus = ObjectStatus | 'pending' | 'rejected'

type UpdateMedia = Partial<Omit<CreateMedia, 'id'> & { files: File[] }>
type CreateMedia = Omit<MediaItem, 'size' | 'hasAudio' | 'createdts' | 'authors' | 'publishedBy' | 'rejectedBy' | 'tags'>

interface Slide {
  id: string
  srcSet: SourceSet
  type?: string
  aspectRatio?: number
  caption?: TranslatedString
  name?: string
  order?: number
  file?: File
  percentage?: number
}

interface SocialImage {
  url: string
  dimensions: Dimensions
  size: number
}

type Audio = {
  type: string
  url?: string
  duration?: number
}

interface MediaItem extends ObjectItem {
  status: MediaItemStatus

  years?: numbers[]

  type: 'video' | 'slideshow'
  uploadId?: string
  eventIds?: string[]
  percentage?: number
  mediaStatus?: string // ex. vimeo status: 'available' | 'uploading' | 'transcoding'

  slides: Slide[]
  uri?: string
  aspectRatio: number
  duration: number
  vimeoPlayerEmbedUrl?: string

  sourceUrl: string
  size: number
  hasAudio: boolean
  sourceCreated?: [number, number?]

  pointOfInterestTrack?: PointOfInterestKeyFrame[]
  socialImage?: SocialImage
  thumbnailSrcSet?: SourceSet
  caption?: TranslatedString
  audio?: Audio
  copyrightConfirmed?: boolean

  firstPublished?: number
  publishedId?: string
  rejectedOn?: number
  rejectedId?: string

  publishedBy?: Profile
  rejectedBy?: Profile
}

interface VimeoMediaItem {
  mediaStatus?: string // ex. vimeo status: 'available' | 'uploading' | 'transcoding'
  vimeoPlayerEmbedUrl?: string
  downloadUrl?: string
  aspectRatio?: number
  duration?: number
  hasAudio?: boolean
  thumbnailSrcSet?: SourceSet
  thumbnailAspectRatio?: number
}

type UpdateFn = (item: MediaItem) => void
type SaveFn = (item: MediaItem) => void | Promise<void>

interface UploadCallbacks {
  onReady?: (items: MediaItem[]) => void
  onUpdate?: UpdateFn
  onProgress?: () => void
}

type PlayProgress = {
  duration: number
  percent: number
  seconds: number
}

interface MediaItemQuery extends ObjectItemQuery {
  contributorId?: string | null
  profileId?: string | null
  eventId?: string | null
  notEventId?: string | null
  tagId?: string | null
  status?: MediaItemStatus | 'none'
  title?: string | null
  locale?: string | null
}
