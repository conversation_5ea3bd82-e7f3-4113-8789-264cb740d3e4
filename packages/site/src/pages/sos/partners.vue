<script setup lang="ts">
definePageMeta({
  layout: 'exhibition',
})
type Partner = { title: TranslatedString, logo: string, id: string, partnerType?: string }
const partners: Partner[] = [
  {
    title: { en: 'V. N. <PERSON>zin Kharkiv National University' },
    logo: '01-<PERSON><PERSON>-logo.webp',
    id: 'cdf2b4a6-6fe9-4f08-a6c1-034387f8592c',
  },
  {
    title: { en: 'The Danish Cultural Institute' },
    logo: '02-Dansk-Kulturinstitut-logo.svg',
    id: '8a842209-6a40-4309-8ad9-d2945cc388e7',
  },
  {
    title: { en: '<PERSON> Kunststiftung' },
    logo: '03-EVS_UKRAINE-logo.svg',
    id: '25283b4a-2771-46e4-a66e-d8a13ffd5d2e',
  },
  {
    title: { en: 'Nordic Council of Ministers Office in Lithuania' },
    logo: '04-NCM-Lithuania-logo.svg',
    id: '940c5a00-4ff6-4264-a625-7f142e4b89e2',
  },
  {
    title: { en: 'Goethe-Institut Ukraine' },
    logo: '05-GI-Logo.svg',
    id: 'e05dc4ca-43e1-4c8f-b836-c651f5988c7e',
  },
  {
    title: { en: 'Nürnberger Haus' },
    logo: '06-NH-logo.svg',
    id: '30dcd05d-9c53-4701-824d-13663d7b43ad',
  },
  {
    title: { en: 'Embassy of the Kingdom of the Netherlands in Ukraine' },
    logo: '07-RO_KN-logo.svg',
    id: '758a33f1-b3f5-46d2-839d-d46fa23e8bb2',
  },
  {
    title: { en: 'Grynyov Art Collection' },
    logo: '08-GAC-logo.png',
    id: '6858f812-c98b-4afd-ab12-6fb629a4192f',
  },
  {
    title: { en: 'Stedley Art Foundation' },
    logo: '09-Stedley-logo.png',
    id: '4fe94309-ad2b-4095-af0d-166ec117b24c',
  },
  {
    title: { en: 'FrontPictures' },
    logo: 'a-01-FP-logo.svg',
    id: '6665cfe9-42b7-4be3-a537-d28eccf63af4',
    partnerType: 'technical',
  },
  {
    title: { en: 'Kharkiv Literature Museum' },
    logo: 'b-02-litmuseum-ENG-logo.svg',
    id: 'aaaba4e8-d254-4253-bdcf-165ce79170ef',
    partnerType: 'exhibition',
  },
]
const partnerProfiles: (Partial<Profile> & Partner)[] = []
const profiles = await useFetchProfiles().fetch({ status: 'public', ids: partners.map(p => p.id) })
for (const partner of partners) {
  const profile = profiles.find(p => p.id === partner.id) ?? {}
  partnerProfiles.push({ ...partner, ...profile })
}
const { locale } = useLocale()
</script>

<template>
  <div class="partners-wrap">
    <template v-if="locale !== 'uk'">
      <h2>Project Realized Through Partnership</h2>
      <p>We extend our gratitude to the partners who made this project possible</p>
    </template>
    <template v-else>
      <h2>Проєкт реалізований завдяки партнерству</h2>
      <p>Ми висловлюємо подяку партнерам, які зробили цей проєкт можливим</p>
    </template>
    <div class="partners">
      <template v-for="partner in partnerProfiles" :key="partner.id">
        <UiPopup class="sos-author">
          <template #trigger="{ active }">
            <div class="sos-partner-box" :class="{ active }">
              <img :src="`/p-logos/${partner.logo}`">
            </div>
          </template>
          <div class="sos-pop-description">
            <SosProfileCard :item="partner" with-country class="h2" />
            <Component
              :is="contentToVNode(getTranslation(partner.description))"
              v-if="partner.description"
            />
            <a v-if="partner.link" href="partner.link" target="_blank">
              {{ partner.link }}
            </a>
          </div>
        </UiPopup>
      </template>
    </div>
  </div>
</template>

<style scoped>
.partners-wrap {
  --_color-sos-panel: hsl(from var(--color-sos-panel) calc(h + 250) calc(s - 10) l);

  --color-bg: var(--_color-sos-panel);
  --color-fg: black;
  --_logo-w: 120px;
  --_logo-gap: var(--base-size);
  --_partners-w: min(100%, var(--_logo-w) * 4 + var(--_logo-gap) * 3 );

  display: grid;
  grid-template-columns: auto max(60vw, var(--width-text-block));
  margin: 0 var(--padding-base) var(--padding-big);
  color: black;

  & > *:not(.player) {
    grid-column: 2 / 3;
    z-index: 1;
  }

  h2, p {
    background-color: var(--color-bg);
    text-wrap: balance;
    padding-inline: var(--padding-base);
    width: var(--_partners-w);
  }
  h2 {
    padding-block: var(--padding-base);
    width: fit-content;
  }
}
.partners {
  display: flex;
  flex-wrap: wrap;
  gap: var(--_logo-gap);
  align-items: center;
  justify-content: center;
  width: var(--_partners-w);
  background-color: var(--color-bg);
  padding: var(--_logo-gap);

  .pop-trigger {
    &:hover, &:focus {
      background-color: transparent;
    }
  }

  .sos-partner-box {
    position: relative;
    width: var(--_logo-w);
    aspect-ratio: 13 / 10;
    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: contain;
      mix-blend-mode: multiply;
    }
    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      translate: -50% -50%;
      width: 120%;
      aspect-ratio: 1 / 1;
      border-radius: 50%;
      background-color: transparent;
      transition: background-color 0.3s;
      z-index: -1;
    }
    &:hover, &:focus {
      &:after {
        background-color: color-mix(in lab, var(--color-bg), white 15%);
      }
    }
    &.active {
      &:after {
        background-color: color-mix(in lab, var(--color-bg), white 35%);
      }
    }
  }
}
</style>

<style>
.partners {
  :is(.pop-trigger):not(.router-link-active, :has(.active)) {
    &:hover, &:focus {
      background-color: transparent;
    }
  }
  .pop-trigger:has(.active) {
    background-color: transparent;
    &:hover, &:focus {
      background-color: transparent;
    }
  }
}
</style>
