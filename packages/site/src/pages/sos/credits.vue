<script setup lang="ts">
definePageMeta({
  layout: 'exhibition',
})
const { public: { exhibitionId: id } } = useRuntimeConfig()
const { record: event, load } = useFetchEventItems().query(() => ({ id }))
const credits = computed(() => event.value?.credits)

await load()
</script>

<template>
  <div>
    <!-- <SosEventCardMain class="header-sticky" /> -->
    <SosPageWrap class="sos-credits">
      <SosCredits v-if="credits" :credits="credits" />
    </SosPageWrap>
  </div>
</template>

<style>
.sos-credits {
  --_color-sos-panel: hsl(from var(--color-sos-panel) calc(h + 180) s l);
  --color-fg: black;

  .sos-event-card-main {
    width: fit-content;
  }
  .credits-record, .group-title {
    background-color: var(--_color-sos-panel);
    max-width: var(--width-text-block);
    width: fit-content;
    padding: var(--padding-small);
    color: black;
  }
  .credits-group {
    margin-bottom: var(--padding-small) !important;
    > *:last-child {
      padding-bottom: var(--padding-big) !important;
    }
  }
  .role,
  .sos-profile-title {
    margin-bottom: var(--padding-small);
  }
}
</style>
