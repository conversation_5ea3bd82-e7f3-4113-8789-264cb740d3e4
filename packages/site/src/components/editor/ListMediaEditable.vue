<script setup lang="ts">
import { vOnClickOutside } from '@vueuse/components'
import Spinner from '~/components/ui/Spinner.vue'
import MediaItemSlideshow from '~/components/editor/MediaItemSlideshow.vue'
import MediaItemVideo from '~/components/editor/MediaItemVideo.vue'
import MessageConfirmSubmission from '~/components/messages/MessageConfirmSubmission.vue'
import MessagePreSubmitForm from '~/components/messages/MessagePreSubmitForm.vue'

defineProps<{
  items: MediaItem[]
  playlist: string
}>()

const processing = ref(false)
const selectedItem = ref('')
const showSubmittedForReviewMessage = ref(false)
const preSubmittedItem = ref<MediaItem | null>(null)

const { save } = useFetchMediaItems()

const submitForReview = async (item: MediaItem) => {
  preSubmittedItem.value = null
  processing.value = true
  save(item, {
    status: 'pending',
    copyrightConfirmed: true,
  })
  processing.value = false
  showSubmittedForReviewMessage.value = true
}

const deselect = (itemId?: string) => {
  if (selectedItem.value === itemId) {
    selectedItem.value = ''
  }
}
</script>

<template>
  <ClientOnly>
    <div class="container">
      <span v-if="!items.length && !processing" class="theme message-centered">No Media</span>
      <div class="container-timeline">
        <div v-for="item in items" :key="item.uploadId || item.id" class="media-item-box-wrap">
          <MediaItemVideo
            v-if="item.type === 'video'"
            v-on-click-outside="() => deselect(item.uploadId || item.id)"
            :value="item"
            :selected="item.id === selectedItem"
            :playlist="playlist"
            @click="selectedItem = item.uploadId || item.id || ''"
            @submit-for-review="preSubmittedItem = item"
          />
          <MediaItemSlideshow
            v-if="item.type === 'slideshow'"
            v-on-click-outside="() => deselect(item.uploadId || item.id)"
            :value="item"
            :selected="item.id === selectedItem"
            :playlist="playlist"
            @click="selectedItem = item.uploadId || item.id || ''"
            @submit-for-review="preSubmittedItem = item"
          />
        </div>
      </div>
      <Spinner v-if="processing" />
      <MessagePreSubmitForm
        v-if="preSubmittedItem"
        @close="preSubmittedItem = null"
        @submit="submitForReview(preSubmittedItem)"
      />
      <MessageConfirmSubmission v-if="showSubmittedForReviewMessage" @close="showSubmittedForReviewMessage = false" />
    </div>
  </ClientOnly>
</template>

<style lang='scss'>
.container {
  --media-box-size: calc(var(--base-size) * 4);
  pointer-events: none;

  .container-timeline {
    margin: var(--padding-small) 0;
    display: flex;
    flex-flow: wrap;
    align-items: center;
    gap: var(--padding-base);
    pointer-events: none;

    & * {
      pointer-events: auto;
    }
  }
}
</style>
