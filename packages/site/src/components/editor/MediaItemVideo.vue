<script setup lang="ts">
import MediaItemEditorCard from '~/components/editor/MediaItemEditorCard.vue'
import PlayerPlaybar from '~/components/player/PlayerControlBar.vue'
import PlayerVideo from '~/components/player/PlayerVideo.vue'

const props = defineProps<{
  value: MediaItem
  selected: boolean
  playlist: string
}>()

const emit = defineEmits(['submit-for-review', 'muted', 'ended'])

const { isAdmin, user } = useUserStore()
const { save } = useFetchMediaItems()

const player = ref<InstanceType<typeof PlayerVideo> | null>(null)
const sourceUrl = computed(() => props.value.sourceUrl)

const playbackProgress = ref<PlayProgress | null>(null)
const playbackPaused = ref(true)
const buffering = ref(false)
const refreshing = ref(false)

const videoStatus = ref<string | undefined>()
const mediaStatus = computed({
  get() {
    return videoStatus.value ?? props.value.mediaStatus
  },
  set(v: string | undefined) {
    videoStatus.value = v
  },

})
let timeout = 0

onMounted(checkVimeoStatus)
onBeforeUnmount(removeTimeout)

watch(() => props.value, checkVimeoStatus, { deep: true })

function togglePlay(value: boolean) {
  player.value?.togglePlay(value)
}

async function checkVimeoStatus() {
  removeTimeout()
  if (props.value && mediaStatus.value && ['uploading', 'transcode_starting', 'transcoding'].includes(mediaStatus.value)) {
    await getVimeoData()
    if (!mediaStatus.value || !['available', 'error'].includes(mediaStatus.value)) {
      timeout = window.setTimeout(checkVimeoStatus, 10000)
    }
  }
}

async function getVimeoData() {
  const { id, uri } = props.value
  if (id && uri) {
    refreshing.value = true
    try {
      const vimeoData = await $fetch('/api/vimeo/get', { method: 'POST', body: { uri: uri } })
      mediaStatus.value = vimeoData.mediaStatus
      if (vimeoData.mediaStatus === 'available') {
        save(props.value, vimeoData, user.value?.id)
      }
    }
    catch (err) {
      mediaStatus.value = 'error'
      console.error(err)
    }
    refreshing.value = false
  }
}

function removeTimeout() {
  if (timeout) {
    window.clearTimeout(timeout)
    timeout = 0
  }
}
</script>

<template>
  <MediaItemEditorCard
    :value="value"
    :selected="selected"
    :playlist="playlist"
    @submit-for-review="emit('submit-for-review')">
    <PlayerVideo
      v-if="sourceUrl && mediaStatus === 'available'"
      ref="playerRef"
      :item="value"
      class="player-aspect-ratio"
      fit-to-box
      @bufferstart="buffering = true"
      @bufferend="buffering = false"
      @paused="playbackPaused = $event"
      @pause="playbackPaused = true"
      @play="playbackPaused = false"
      @timeupdate="playbackProgress = $event"
      @ended="emit('ended')"
    />
    <template #player>
      <PlayerPlaybar
        :duration="value.duration"
        :buffering="buffering"
        :playback-progress="playbackProgress"
        :paused="playbackPaused"
        class="small"
        @toggle-play="togglePlay"
      />
    </template>
    <template #buttons>
      <button v-if="isAdmin" class="compact" @click="getVimeoData">
        <span class="icon-asterisk small" :class="{ spin: refreshing }" />
      </button>
    </template>
  </MediaItemEditorCard>
</template>

<style lang="scss">
.spin {
  animation: spin 0.5s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
