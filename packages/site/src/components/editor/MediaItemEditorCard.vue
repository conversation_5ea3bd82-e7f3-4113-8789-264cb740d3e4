<script setup lang="ts">
import MediaItemDescription from '~/components/player/MediaItemDescription.vue'
import UpdateSocialImageButton from '~/components/editor/UpdateSocialImageButton.vue'
import TVPlaceholder from '~/components/TV/TVPlaceholder.vue'
import RandomColorsButton from '~/components/editor/RandomColorsButton.vue'
import Spinner from '~/components/ui/Spinner.vue'
import { secondsToHHMMSS } from '~/lib/seconds-to-timecode'

const props = defineProps<{
  value: MediaItem
  selected: boolean
  playlist: string
}>()

const emit = defineEmits(['submit-for-review'])

const { showMediaEditor } = useRootStore()
const { isEditor, isSMM } = useUserStore()
const { trash } = useFetchMediaItems()

const percentage = computed(() => props.value.percentage ?? 0)
const duration = computed(() => secondsToHHMMSS(props.value.duration ?? 0))
const processing = computed(() => !props.value.id || [undefined, 'resizing', 'transcode_starting', 'transcoding'].includes(props.value.mediaStatus))

const statusMessage = computed(() => {
  switch (props.value.mediaStatus) {
    case 'transcode_starting':
    case 'transcoding':
      return 'Transcoding'
    case 'resizing':
      return 'Resizing'
    case 'uploading':
      return 'Uploading'
    default:
      return ''
  }
})

const statusEditedBy = computed(() => {
  const profile = props.value.rejectedBy ?? props.value.publishedBy
  if (!profile) {
    return ''
  }
  return getTranslation(profile?.title)
})

const goToPreview = async () => {
  await navigateTo({ name: 'works-id', params: { id: props.value.id }, query: { p: props.playlist } })
}
</script>

<template>
  <div class="media-item-card" :class="{ selected }">
    <div class="media-item-view">
      <TVPlaceholder v-if="value.mediaStatus !== 'available'" :class="{ grayscale: !value.id }" />
      <Spinner v-if="processing" :class="{ 'with-message': statusMessage }">
        <span v-if="statusMessage">{{ statusMessage }}</span>
      </Spinner>
      <div v-else-if="percentage > 0 && percentage < 100" class="media-item-upload-progress">
        <div class="progress-bar" :style="{ width: percentage.toFixed(2) + '%' }" />
        <span>{{ percentage.toFixed(2) }}%</span>
      </div>
      <slot />
    </div>
    <div class="media-item-info">
      <slot name="player" />
      <button class="theme" @click="goToPreview">
        Preview
      </button>
      <MediaItemDescription :media-item="value" class="media-item-info-title" />
      <footer>
        <div class="footer-row">
          <div>{{ duration }}</div>
          <div class="media-item-visibility-indicator" :class="[value.status]">
            <div>{{ value.status }}</div>
          </div>
        </div>
        <div v-if="value.status === 'draft' && (value.mediaStatus === 'available')" class="submit-for-review-wrap">
          <button class="submit-for-review media-item-invert-colors" @click="emit('submit-for-review')">
            Submit for Review
          </button>
        </div>
        <div
          v-if="isEditor && statusEditedBy"
          class="media-item-visibility-indicator status-edited-by"
          :class="[value.status]">
          {{ statusEditedBy }}
        </div>
      </footer>
    </div>
    <header>
      <button
        v-if="selected && (value.status === 'draft' || value.status === 'rejected' || isEditor)"
        class="compact remove"
        @click="trash(value)">
        <span class="icon-trash small" />
      </button>
      <div class="flex-row-center">
        <UpdateSocialImageButton v-if="isEditor" :item="value" type="media" />
        <RandomColorsButton v-if="isSMM" :item="value" type="media" class="theme" />
        <button
          v-if="(!percentage || percentage === 100) && !processing && (value.status === 'draft' || isEditor)"
          class="compact edit"
          @click="showMediaEditor(value)">
          <span class="icon-pencil small" />
        </button>
      </div>
      <slot name="buttons" />
    </header>
  </div>
</template>

<style lang="scss">
.media-item-card {
  position: relative;
  width: var(--media-box-size, calc(var(--base-size) * 4));
  background-color: var(--color-bg, #000);
  color: var(--color-fg, #fff);

  &.selected {
    box-shadow: 0 0 0 var(--padding-mini) var(--color-bg, #000);

    .submit-for-review-wrap {
      padding: 0;
    }
  }

  .media-item-view {
    position: relative;
    height: var(--media-box-size, calc(var(--base-size) * 4));

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .media-item-upload-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: var(--size-three-quarters);
    background-color: var(--color-fg, #fff);
    width: calc(100% - var(--padding-small));
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;

    &>.progress-bar {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 0%;
      background-color: var(--color-bg, #000);
      transition: width 0.2s;
    }

    &>span {
      color: #fff;
      mix-blend-mode: exclusion;
    }
  }

  .media-item-info {
    font-size: calc(var(--font-size-caption));

    .media-item-info-title {
      margin: var(--padding-mini) 0;

      &>div {
        padding: 0 var(--padding-mini);
      }
    }
  }

  footer {
    display: flex;
    flex-direction: column;

    .footer-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: var(--size-half);
      padding: 0 var(--padding-mini);
      padding-right: 0;
    }

    .submit-for-review-wrap {
      display: flex;
      align-items: center;
      justify-content: center;

      .submit-for-review {
        // font-size: var(--font-size-caption);
        text-transform: uppercase;
        width: 100%;
        padding: var(--padding-mini);
        padding-right: 0;
        font-size: var(--h1);
        font-weight: 300;
        line-height: 1.1;
        white-space: unset;
        height: auto;
      }
    }

    .media-item-visibility-indicator {
      width: min-content;
      height: 100%;
      padding: 0 var(--padding-small);
      display: flex;
      flex-flow: column;
      justify-content: center;
      background: black;
      color: white;
      font-size: var(--font-size-caption);
      text-transform: uppercase;

      &.draft {
        background: var(--color-tv-1);
        color: var(--color-tv-text-1);
      }

      &.public {
        background: var(--color-tv-4);
        color: var(--color-tv-text-4);
      }

      &.rejected {
        background: var(--color-tv-5);
        color: var(--color-tv-text-5);
      }

      &.status-edited-by {
        align-self: flex-end;
        text-transform: unset;
        display: block;
        width: fit-content;

        &::before {
          content: "by ";
          opacity: 0.5;
        }
      }
    }
  }

  header {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    width: 100%;

    button {
      background-color: var(--color-bg, #000);
      color: var(--color-fg, #fff);

      &.remove {
        margin-right: auto;
      }
    }
  }
}
</style>
