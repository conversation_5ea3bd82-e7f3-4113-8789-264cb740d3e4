<script setup lang="ts">
const route = useRoute()
// const showLogo = computed(() => route.name !== 'sos-network')
</script>

<template>
  <div class="sense-of-safety">
    <SosStream />
    <SosMenu />
    <SosDonateButton />
    <SosEventCardMain
      class="header-sticky"
      :class="{
        compact: route.name === 'sos-about' || route.name === 'sos-network' || route.name === 'sos-event-id',
      }">
      <UiCollapsibleBox
        :class="{ open: route.name === 'sos-network' || route.name === 'sos-event-id' }">
        <SosBridgesLogo />
      </UiCollapsibleBox>
    </SosEventCardMain>
    <slot />
  </div>
</template>

<style>
@property --drop-shadow-size {
  initial-value: 0;
  inherits: true;
  syntax: '<length>';
}

.sos-donate-button {
  position: fixed;
  right: var(--padding-small);
  bottom: var(--padding-small);
  z-index: 2000;
}
.stream-box {
  position: fixed;
  inset: 0;
}
</style>
