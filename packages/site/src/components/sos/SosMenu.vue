<script setup lang="ts">
import { ref } from 'vue'

const { locale } = useLocale()
const isMenuOpen = ref(false)

const menuItems = [
  {
    to: { name: 'sos' },
    label: {
      en: 'Home',
      uk: 'Home',
    },
  },
  {
    to: { name: 'sos-exhibits' },
    label: {
      en: 'Artworks',
      uk: 'ТВОРИ',
    },
  },
  {
    to: { name: 'sos-network' },
    label: {
      en: 'Bridges',
      uk: 'Мости',
    },
  },
  {
    to: { name: 'sos-about' },
    label: {
      en: 'About',
      uk: 'Про проект',
    },
  },
  {
    to: { name: 'sos-credits' },
    label: {
      en: 'credits',
      uk: 'Команда',
    },
  },
  {
    to: { name: 'sos-partners' },
    label: {
      en: 'partners',
      uk: 'Партнери',
    },
  },
]

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}
</script>

<template>
  <header class="header-main">
    <div class="menu-container">
      <a class="lnk" href="https://antiwarcoalition.art"><TVStripedText>AWC</TVStripedText></a>
      <!-- Burger menu button for mobile -->
      <button class="burger-button" @click="toggleMenu">
        <div>
          <span />
          <span />
          <span />
        </div>
      </button>

      <nav :class="{ 'is-open': isMenuOpen }">
        <NuxtLink
          v-for="{ to, label } in menuItems"
          :key="to.name"
          :to="to"
          class="menu-item"
          @click="isMenuOpen = false">
          <span>{{ getTranslation(label, locale) }}</span>
        </NuxtLink>
      </nav>
    </div>
    <LanguageSwitch />
  </header>
</template>

<style scoped>
.header-main {
  --color-bg: var(--color-sos-main);
  position: sticky;
  top: 0;
  height: var(--base-size);
  z-index: 2000;
  display: flex;
  justify-content: space-between;
  a {
    text-decoration: none;
  }
}

.menu-container {
  display: flex;
  /* align-items: center; */
  align-items: stretch;
}

nav {
  display: flex;
  width: fit-content;
  backdrop-filter: blur(10px);
  background-color: var(--color-sos-main);
  align-self: stretch;
}

.menu-item {
  font-size: var(--font-size-h2);
  text-transform: uppercase;
  color: var(--color-sos-text);
  display: flex;
  align-items: center;
  justify-content: center;
  padding-inline: var(--padding-base);
  text-decoration: none;
}

.router-link-active {
  background-color: var(--color-sos-text);
  color: var(--color-sos-main);
}

.burger-button {
  display: none;
  width: var(--base-size);
  height: var(--base-size);
  padding: 0;
  z-index: 10;
  background-color: var(--color-sos-main);
  align-items: center;
  justify-content: center;

  & > div {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: var(--padding-big);
    height: var(--padding-big);
    transition: height 0.2s linear;

    span {
      width: 100%;
      height: var(--padding-mini);
      background: var(--color-sos-text);
      position: relative;
      transform-origin: 1px;
    }
  }
  &:active {
    & > div {
      height: var(--padding-base);
    }
  }
}

@media (max-width: 768px) {
  .burger-button {
    display: flex;
  }

  nav {
    position: fixed;
    top: var(--base-size);
    left: 0;
    height: fit-content;
    width: fit-content;
    flex-direction: column;
    margin-left: 0;
    transform: translateX(-100%);
    transition: transform 0.2s ease-in-out;

    .menu-item {
      height: var(--base-size);
      width: 100%;
      justify-content: flex-start;
    }
  }

  nav.is-open {
    transform: translateX(var(--base-size));
  }
}
</style>
