<script setup lang="ts">
defineProps<{
  theme?: Theme
  withCloseButton?: boolean
}>()
// const router = useRouter()
// function close() {
//   router.back()
// }
const pageFooterHeight = ref(0)
onMounted(() => {
  setTimeout(() => {
    const donateButton = document.querySelector('.sos-donate-button')
    if (donateButton) {
      const { top } = donateButton.getBoundingClientRect()
      pageFooterHeight.value = window.innerHeight - top
    }
  }, 100)
})
const emit = defineEmits(['request-close'])
</script>

<template>
  <div class="sos-page-wrap" :style="{ ...theme, '--footer-h': `${pageFooterHeight}px` }">
    <div class="click-pad" @click="emit('request-close')" />
    <button v-if="withCloseButton" class="compact close" @click="emit('request-close')">
      <span class="icon-close big" />
    </button>
    <!-- <div class="sos-page-content"> -->
    <slot />
    <!-- </div> -->
  </div>
</template>

<style lang='scss'>
.sos-page-wrap {
  --page-header-h: var(--base-size);
  --page-footer-h: max(var(--base-size), var(--footer-h, 0px));
  --page-margin-i: clamp(var(--padding-small), 2cqi, var(--padding-big));

  position: relative;
  display: grid;
  grid-template-columns: auto max(60vw, var(--width-text-block));
  margin-inline: var(--page-margin-i);
  margin-block-end: calc(var(--base-size) * 2 + var(--padding-small));

  // @container (width < calc(40rem + 2rem)) {
  //   margin-inline: 0;
  // }

  & > :not(.click-pad, .full-width-content) {
    grid-column: 2 / 3;
    z-index: 1;
  }

  .click-pad {
    position: absolute;
    inset: 0;
    grid-column: 1 / 3;
  }

  .full-width-content {
    grid-column: 1 / 3;
  }

  & > :not(
    section,
    button.close,
    .click-pad,
    .full-width-content,
    .slideshow-footer,
    .sos-slides-controls,
    .credits-record),
  & section > :not(section) {
    max-width: var(--width-text-block);
    min-width: min(100%, 350px);
    width: fit-content;
    background-color: var(--color-bg);
    color: var(--color-fg);
    margin: 0;
    padding: var(--padding-small);
    padding-right: var(--padding-big);
    text-wrap: balance;

    & button.popup-trigger {
      margin-bottom: 0;
    }
    &.player-controls,
    &.media-viewer {
      padding: 0;
      width: 100%;
      max-width: unset;
      position: sticky;
      top: 0;
      background-color: transparent;
    }
  }
  .media-viewer {
    position: sticky;
    top: 0;
    background-color: transparent;
  }
  .sos-slides-controls {
    --button-size: var(--base-size);
    margin-top: calc(var(--base-size) * -1);
    margin-left: auto;
    position: sticky;
    top: var(--button-size);
  }

  button.close {
    position: sticky;
    top: 0;
    right: 0;
    z-index: 1000;
    margin-left: auto;
    margin-bottom: calc(var(--button-size) * -1);
  }
}
</style>
