export default defineEventHandler(async (event) => {
  const query = getQuery<EventItemQuery>(event)
  query.status ??= 'public'
  if (query.status !== 'public') {
    const { isEditor } = await requireUserSession(event)
    if (!isEditor) {
      query.status = 'public'
    }
    else if (query.status === 'none') {
      query.statuses = ['draft', 'public']
    }
  }
  return useDb().eventItems.query(query)
})
