import { type DecodedIdToken, FlarebaseAuth } from '@marplex/flarebase-auth'
import type { CookieSerializeOptions } from 'cookie-es'
import type { H3Event } from 'h3'

export const SESSION_NAME = 'session'
export const DEFAULT_COOKIE: CookieSerializeOptions = {
  path: '/',
  secure: true,
  httpOnly: true,
}

const roles: Record<UserRole, UserRole[]> = {
  admin: ['admin'],
  editor: ['admin', 'editor'],
  contributor: ['admin', 'editor', 'contributor'],
  smm: ['admin', 'editor', 'smm'],
}

export function useAuth() {
  const { public: { firebase: { projectId, apiKey } }, firebase: { serviceAccountEmail } } = useRuntimeConfig()
  return new FlarebaseAuth({
    projectId,
    apiKey,
    privateKey: useEnv('FIREBASE_PRIVATE_KEY'),
    serviceAccountEmail,
  })
}

export function toUserInfo({ name = '', user_id = '', role = 'contributor' }: DecodedIdToken & { name?: string, user_id?: string, role?: UserRole }): Profile {
  return {
    id: user_id,
    status: 'draft',
    type: 'person', // TODO: set a correct type?
    role,
    title: { en: name },
    created: 0,
    isAdmin: roles.admin.includes(role),
    isEditor: roles.editor.includes(role),
    isContributor: roles.contributor.includes(role),
    isSmm: roles.smm.includes(role),
  }
}

export async function getUserSession(event: H3Event) {
  const cookie = getCookie(event, SESSION_NAME)
  if (cookie) {
    try {
      return toUserInfo(await useAuth().verifySessionCookie(cookie))
    }
    catch (error) {
      console.warn('USER SESSION ERROR', error)
    }
  }
}

interface UserSessionParams {
  role?: UserRole
  record?: {
    status?: 'draft' | string
    contributorIds?: string[]
  }
  statusCode?: number
  message?: string
}

export async function requireUserSession(
  event: H3Event,
  {
    role = 'contributor',
    record,
    statusCode = 401,
    message = 'Unauthorized',
  }: UserSessionParams = {},
) {
  const user = await getUserSession(event)
  if (user && roles[role].includes(user.role)) {
    if (user.role === 'admin' || user.role === 'editor') {
      return user
    }
    if (record && (record.status === 'draft' && !(record.contributorIds ?? []).includes(user.id))) {
      throw createError({
        statusCode,
        message,
      })
    }
    return user
  }
  throw createError({
    statusCode,
    message,
  })
}
