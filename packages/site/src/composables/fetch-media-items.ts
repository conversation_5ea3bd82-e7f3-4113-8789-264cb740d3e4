import { cloneDeep, flatten, merge, omit, pickBy, uniq, without } from 'lodash-es'
import type { MultiWatchSources } from 'vue'
import { uploadContent } from '~/lib/media'
import { diff, removeUndefined } from '~/lib/object'
import { toUploadableMedia } from '~/lib/to-uploadable-media'

type CompareItem = Pick<MediaItem, 'id' | 'uploadId'>
type QueryFunc = () => MediaItemQuery
type WatchTransform = { watch?: MultiWatchSources, transform?: (item: MediaItem) => MediaItem }

function compare(a: CompareItem) {
  return function (b: CompareItem) {
    if (a.id && b.id) {
      return a.id === b.id
    }
    if (a.uploadId && b.uploadId) {
      return a.uploadId === b.uploadId
    }
    return false
  }
}

const KEY = 'media-items'

export function useFetchMediaItems() {
  const items = useState<MediaItem[]>(KEY, () => [])
  const loaded = useState<boolean>(`${KEY}-loaded`, () => false)
  const publicLoaded = useState<boolean>(`${KEY}-public-loaded`, () => false)

  const route = useRoute()
  const headers = useRequestHeaders(['cookie'])
  const { user } = useUserStore()
  const tagsStore = useFetchTags()
  const profilesStore = useFetchProfiles()

  function filter({ status, id, ids, tagId, contributorId, profileId, eventId }: MediaItemQuery) {
    let records = items.value?.slice() ?? []
    if (status && status !== 'none') {
      records = records.filter(r => r.status === status)
    }
    if (id) {
      records = records.filter(r => r.id === id)
    }
    else if (ids?.length) {
      records = records.filter(r => ids?.includes(r.id))
    }
    if (tagId) {
      records = records.filter(({ tagIds }) => tagIds?.includes(tagId))
    }
    if (contributorId) {
      records = records.filter(({ contributorIds }) => contributorIds?.includes(contributorId))
    }
    if (profileId) {
      records = records.filter(({ creditsIds }) => uniq(flatten(creditsIds?.map(({ profileIds }) => profileIds)))?.includes(profileId))
    }
    if (eventId) {
      records = records.filter(({ eventIds }) => eventIds?.includes(eventId))
    }
    return records
  }

  async function got(query: MediaItemQuery) {
    if (loaded.value) {
      return []
    }
    if (query.status === 'public') {
      if (publicLoaded.value) {
        return []
      }
    }
    if (query.id || query.ids) {
      const ids = uniq([...query.ids ?? [], ...(query.id ? [query.id] : [])])
      query.ids = without(ids, ...(items.value?.map(({ id }) => id) ?? []))
      if (query.ids.length === 0) {
        return []
      }
      delete query.id
    }
    return $fetch<MediaItem[]>('/api/media-items', { query, headers })
  }

  async function load(query: MediaItemQuery = {}, transform?: WatchTransform['transform']) {
    let records = await got(cloneDeep(query))
    if (transform) {
      records = records.map(transform)
    }

    loaded.value ||= Object.keys(pickBy(query)).length === 1 && query.status === 'none'
    publicLoaded.value ||= Object.keys(pickBy(query)).length === 1 && query.status === 'public'
    await setItems(records)
    return filter(query)
  }

  async function setItems(records: MediaItem[]) {
    await setCreditsItem(records, ids => profilesStore.fetch({ ids }))
    await setTags(records, ids => tagsStore.fetch({ ids }))

    const profileIds = uniq(flatten(records.map(({ rejectedId, publishedId }) => [...(rejectedId ? [rejectedId] : []), ...(publishedId ? [publishedId] : [])])))
    const profiles = await profilesStore.fetch({ ids: profileIds })
    for (const record of records) {
      if (record.rejectedId) {
        record.rejectedBy = profiles.find(({ id }) => id === record.rejectedId)
        delete record.rejectedId
      }
      if (record.publishedId) {
        record.publishedBy = profiles.find(({ id }) => id === record.publishedId)
        delete record.publishedId
      }
    }
    Collection.set(items, records, compare)
  }

  async function init(records: MediaItem[]) {
    publicLoaded.value = true
    await setItems(records)
  }

  async function uploadMedia(userId: string, files: File[]) {
    const id = crypto.randomUUID()
    const process = async () => {
      const uploadableJobs = toUploadableMedia(files, id, userId)
      for (const job of uploadableJobs) {
        const item = await $fetch<MediaItem>('/api/media-items', { method: 'POST', body: job.toMediaItem() })
        await setItems([item])

        await job.start(item => setItems([item]), async (data) => {
          const body = removeUndefined(data)
          const item = await $fetch<MediaItem>(`/api/media-items/${id}`, { method: 'PATCH', body })
          await setItems([item])
        })
      }
    }
    useNuxtApp().$jobs.run({ id, process })
  }

  async function update({ description, ...data }: UpdateMedia, files: File[], item: MediaItem) {
    const body: UpdateMedia = removeUndefined(omit(diff(item, cloneDeep(data)), 'modified'))
    await setItems([merge(item, body)])
    if (description) {
      body.description = await uploadContent(item.id, description, files)
    }
    const newItem = await $fetch<MediaItem>(`/api/media-items/${item.id}`, { method: 'PATCH', body })
    await setItems([newItem])
  }

  function save(item: MediaItem | null | undefined, { files = [], ...data }: UpdateMedia, userId: string) {
    const id = item?.id ?? crypto.randomUUID()
    const process = () => item ? update(data, files, item) : uploadMedia(id, userId, files)
    useNuxtApp().$jobs.run({ id, process })
  }

  async function trash(item: CompareItem) {
    Collection.remove(items, [item], compare)
    useNuxtApp().$jobs.run({
      id: item.id,
      async process() {
        $fetch<MediaItem>(`/api/media-items/${item.id}`, { method: 'PATCH', body: { status: 'trash' } })
      },
    })
  }

  return {
    init,
    query: (queryFn?: QueryFunc, { watch, transform }: WatchTransform = {}) => ({
      records: computed(() => filter(queryFn?.() ?? {})),
      record: computed(() => filter(queryFn?.() ?? {}).slice().shift()),
      load: () => useAsyncData<MediaItem[]>(KEY, async () => load(queryFn?.() ?? {}, transform), { watch: watch ?? [user, () => route.fullPath] }),
    }),
    uploadMedia,
    save,
    trash,
  }
}
