<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
  <EditorPopover v-if="open" id="main-editor" />
</template>

<script setup lang="ts">
useHead({
  script: [
    {
      'src': 'https://static.cloudflareinsights.com/beacon.min.js',
      'defer': true,
      'data-cf-beacon': '{"token": "267732ba776c4e3a9e29a4a0e2667794"}',
    },
  ],
})

onMounted(() => {
  window.addEventListener('resize', fixIOSBag)
  fixIOSBag()
})

const { showEditor } = useRootStore()
const { user } = useUserStore()
const open = computed(() => Bo<PERSON>an(user.value) && showEditor.value !== undefined)

function fixIOSBag() {
  document.documentElement.style.setProperty('--win-height', `${window.visualViewport?.height ?? window.innerHeight}px`)
}
</script>
