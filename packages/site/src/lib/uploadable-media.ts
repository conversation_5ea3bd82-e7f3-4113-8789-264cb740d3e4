type MediaStatus = 'unknown' | 'resizing' | 'uploading' | 'available' | 'transcode_starting' | 'error' | VimeoVideoStatus

export abstract class UploadableMedia {
  mediaStatus: MediaStatus
  readonly created: number

  constructor(readonly type: 'video' | 'slideshow', readonly id: string, readonly userId: string) {
    this.mediaStatus = 'unknown'
    this.created = Date.now()
  }

  toMediaItem(): MediaItem {
    return {
      id: this.id,
      type: this.type,
      status: 'draft',
      mediaStatus: this.mediaStatus,
      contributorIds: [this.userId],

      title: {},
      theme: randomTheme(),
      created: this.created,
      modified: Date.now(),

      slides: [],
      sourceUrl: '',
      aspectRatio: 0,
      size: 0,
      duration: 0,
      hasAudio: false,
      tagIds: [],
      socialImage: {
        url: '',
        dimensions: {
          w: 0,
          h: 0,
        },
        size: 0,
      },
    }
  }

  abstract start(onUpdateFn: UpdateFn, onSaveFn: SaveFn): Promise<void>
}
