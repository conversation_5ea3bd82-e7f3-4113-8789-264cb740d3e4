# Nuxt.js AI Agent Rules

## Core Principles

### 1. Auto-imports First
- **NEVER manually import** Vue composables (`ref`, `computed`, `watch`, etc.) - they are auto-imported
- **NEVER manually import** Nuxt composables (`useFetch`, `useRoute`, `useRouter`, `useNuxtApp`, etc.)
- **NEVER manually import** components from `~/components/` - they are auto-imported
- **NEVER manually import** utilities from `~/utils/` - they are auto-imported
- **NEVER manually import** composables from `~/composables/` - they are auto-imported
- Only use explicit imports from `#imports` when absolutely necessary for clarity

### 2. Directory Structure Respect
- Use the conventional Nuxt directory structure
- Place files in their appropriate directories:
  - `pages/` for route components
  - `components/` for reusable Vue components
  - `composables/` for Vue composables
  - `utils/` for helper functions
  - `layouts/` for layout components
  - `middleware/` for route middleware
  - `plugins/` for Vue plugins
  - `server/` for server-side code
  - `public/` for static assets
  - `assets/` for build-time assets

### 3. TypeScript by Default
- Always use TypeScript syntax in `.vue`, `.ts`, and `.js` files
- Leverage Nuxt's auto-generated types from `.nuxt/nuxt.d.ts`
- Use `<script setup lang="ts">` in Vue components
- Never manually edit `.nuxt/tsconfig.json` - it's auto-generated

## File-Based Routing Rules

### 4. Pages Structure
- Use file-based routing in `pages/` directory
- Dynamic routes: `[param].vue` for required params, `[[param]].vue` for optional params
- Catch-all routes: `[...slug].vue`
- Nested routes: use directory structure with `<NuxtPage>` component
- Route groups: use `(groupName)/` for organization without affecting URLs

### 5. Page Components
- Pages **MUST have a single root element** for route transitions
- Use `definePageMeta()` for page metadata (layout, middleware, etc.)
- Use `<NuxtPage>` in parent routes to display child routes
- Use `<NuxtLink>` for navigation, not `<router-link>`

## Component Rules

### 6. Component Auto-imports
- Components in `~/components/` are automatically available
- Use PascalCase for component names in templates
- Nested components: `components/base/Button.vue` becomes `<BaseButton>`
- Client-only components: use `.client.vue` suffix
- Server-only components: use `.server.vue` suffix

### 7. Component Structure
- Use `<script setup>` syntax for composition API
- Use `defineProps()` and `defineEmits()` for component interface
- Use `defineExpose()` to expose component methods/properties

## Composables and Utils

### 8. Composables Best Practices
- Create composables in `~/composables/` directory
- Use `use` prefix for composable names (e.g., `useAuth`, `useApi`)
- Composables must be called in the right context (component setup, plugin, middleware)
- **NEVER call composables** outside of Vue setup functions or Nuxt contexts

### 9. Server vs Client Context
- Be aware of server/client rendering differences
- Use `process.client` or `process.server` for environment-specific code
- Use `<ClientOnly>` component for client-only content
- Avoid browser APIs in server-rendered code

## Data Fetching

### 10. Data Fetching Patterns
- Use `useFetch()` for API calls with automatic reactivity
- Use `$fetch()` for imperative API calls
- Use `useAsyncData()` for custom async data fetching
- Use `useLazyFetch()` for non-blocking data fetching
- Always handle loading and error states

## Configuration

### 11. Nuxt Config
- Use `nuxt.config.ts` for configuration
- Leverage route rules for hybrid rendering
- Configure auto-imports in `imports` section if needed
- Use modules for extending functionality

### 12. Environment and Runtime
- Use `useRuntimeConfig()` for runtime configuration
- Use `.env` files for environment variables
- Prefix public runtime config with `NUXT_PUBLIC_`

## Rendering Modes

### 13. Rendering Strategy
- Default to universal rendering (SSR)
- Use `ssr: false` for client-side only apps
- Use route rules for hybrid rendering per route
- Consider edge-side rendering for performance

## Layouts and Middleware

### 14. Layouts
- Create layouts in `~/layouts/` directory
- Use `<NuxtLayout>` component in `app.vue`
- Set layout in `definePageMeta({ layout: 'custom' })`
- Default layout is `layouts/default.vue`

### 15. Middleware
- Create middleware in `~/middleware/` directory
- Use `defineNuxtRouteMiddleware()` for route middleware
- Apply middleware via `definePageMeta({ middleware: 'auth' })`
- Global middleware: use `.global.ts` suffix

## Error Handling

### 16. Error Pages
- Create `error.vue` in project root for custom error pages
- Use `throw createError()` for custom errors
- Handle errors in `useFetch` and `useAsyncData`

## Performance and Optimization

### 17. Bundle Optimization
- Use dynamic imports for code splitting
- Leverage Nuxt's automatic code splitting
- Use `<ClientOnly>` to reduce server bundle size
- Consider lazy loading for heavy components

## Testing and Development

### 18. Development Practices
- Use `nuxt dev` for development
- Use `nuxt build` for production builds
- Use `nuxt typecheck` for type checking
- Test both server and client rendering

## Module and Plugin Integration

### 19. Modules
- Prefer official Nuxt modules over custom implementations
- Configure modules in `nuxt.config.ts`
- Use module auto-imports when available

### 20. Plugins
- Create plugins in `~/plugins/` directory
- Use `.client.ts` or `.server.ts` suffixes for environment-specific plugins
- Use `defineNuxtPlugin()` for plugin definition

## Common Pitfalls to Avoid

### 21. Context Errors
- **NEVER** call composables outside proper context
- **NEVER** use `await` before composables (except in `<script setup>`)
- **NEVER** access `window` or `document` in server context
- **NEVER** manually import auto-imported functions

### 22. Hydration Issues
- Ensure server and client render the same content
- Use `<ClientOnly>` for client-specific content
- Avoid reactive data in `definePageMeta()`

## Code Style

### 23. Vue 3 Composition API
- Prefer `<script setup>` over Options API
- Use `ref()` and `reactive()` for reactivity
- Use `computed()` for derived state
- Use `watch()` and `watchEffect()` for side effects

### 24. Naming Conventions
- Use PascalCase for components
- Use camelCase for composables, variables, and functions
- Use kebab-case for file names (except components)
- Use SCREAMING_SNAKE_CASE for constants

## Advanced Patterns

### 25. State Management
- Use `useState()` for global reactive state
- Use Pinia for complex state management
- Avoid Vuex in Nuxt 3 projects
- Use composables for shared state logic

### 26. SEO and Meta
- Use `useSeoMeta()` for SEO meta tags
- Use `useHead()` for head management
- Set meta in `definePageMeta()` for page-specific meta
- Use `app.head` in `nuxt.config.ts` for global meta

### 27. Internationalization
- Use `@nuxtjs/i18n` module for internationalization
- Structure translations in appropriate directories
- Use `useI18n()` composable for translations
- Configure locale routing in module options

### 28. Authentication
- Use session-based auth with `useSession()`
- Implement auth middleware for protected routes
- Use `navigateTo()` for auth redirects
- Store auth state in `useState()` or Pinia

### 29. API Routes
- Create API routes in `server/api/` directory
- Use proper HTTP methods (GET, POST, PUT, DELETE)
- Use `defineEventHandler()` for API handlers
- Implement proper error handling and validation

### 30. Build and Deployment
- Use `nuxt generate` for static site generation
- Use `nuxt build` for server-side rendering
- Configure `nitro.preset` for deployment targets
- Use environment-specific configurations

## Security Best Practices

### 31. Security Rules
- Validate all user inputs on server-side
- Use CSRF protection for forms
- Implement proper authentication and authorization
- Sanitize data before rendering
- Use HTTPS in production
- Configure proper CORS headers

### 32. Performance Monitoring
- Use `useNuxtApp()` for app context
- Monitor Core Web Vitals
- Use lazy loading for images and components
- Implement proper caching strategies
- Monitor bundle sizes

## Debugging and Troubleshooting

### 33. Common Issues
- Check `.nuxt/` directory for generated types
- Verify auto-import configurations
- Check server vs client context issues
- Validate route structure and naming
- Ensure proper component single root elements

### 34. Development Tools
- Use Vue DevTools for debugging
- Use Nuxt DevTools for development insights
- Use TypeScript strict mode for better type safety
- Use ESLint with Nuxt configuration
- Use Prettier for code formatting

## Migration and Compatibility

### 35. Vue 2 to Vue 3 Migration
- Replace Vue 2 syntax with Vue 3 Composition API
- Update component lifecycle hooks
- Replace `this.$` patterns with composables
- Update event handling syntax
- Use `defineComponent` when needed for compatibility

### 36. Nuxt 2 to Nuxt 3 Migration
- Replace `asyncData` with `useFetch` or `useAsyncData`
- Replace `fetch` with `useLazyFetch`
- Update middleware syntax
- Replace `$nuxt` with `useNuxtApp()`
- Update plugin registration syntax

## Final Guidelines

### 37. Code Quality
- Write self-documenting code
- Use meaningful variable and function names
- Keep components small and focused
- Implement proper error boundaries
- Write unit tests for critical functionality

### 38. Documentation
- Document complex composables and utilities
- Use TypeScript interfaces for data structures
- Comment non-obvious business logic
- Maintain README files for project setup
- Document API endpoints and their usage

Remember: Nuxt 3 is built on modern web standards and Vue 3. Always leverage the framework's conventions and auto-imports rather than fighting against them. The framework is designed to be intuitive and productive when used correctly.
